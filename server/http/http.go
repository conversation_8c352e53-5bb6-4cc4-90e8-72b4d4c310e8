package http

import (
	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	"github.com/Sera-Global/be-nbs-accounting-system/server/http/basic_price"
	"github.com/Sera-Global/be-nbs-accounting-system/server/http/block"
	"github.com/Sera-Global/be-nbs-accounting-system/server/http/customer"
	"github.com/Sera-Global/be-nbs-accounting-system/server/http/distant_fee"
	"github.com/Sera-Global/be-nbs-accounting-system/server/http/health"
	mw "github.com/Sera-Global/be-nbs-accounting-system/server/http/middleware"
	"github.com/Sera-Global/be-nbs-accounting-system/server/http/option"
	"github.com/Sera-Global/be-nbs-accounting-system/server/http/qualification"
	"github.com/Sera-Global/be-nbs-accounting-system/server/http/site_report"
	"github.com/Sera-Global/be-nbs-accounting-system/server/http/user"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func InitHttp() *echo.Echo {
	e := echo.New()
	e.Use(middleware.CORS())
	V1Routes(e.Group("/v1"))

	return e
}

func V1Routes(g *echo.Group) {
	g.GET("/health", health.Check)

	// user
	g.POST("/user/login", user.Login)
	g.GET("/user/worker/list", user.GetWorkerList, mw.JWTVerify([]string{constanta.RoleSupervisor, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))

	// site report
	g.GET("/site-report/list", site_report.GetList, mw.JWTVerify([]string{constanta.RoleAdmin, constanta.RoleSuperAdmin}))
	g.POST("/site-report/bulk-update", site_report.BulkUpdate, mw.JWTVerify([]string{constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))
	g.GET("/site-report/statutory/calculation-variable", site_report.GetStatutoryCalculationVariable, mw.JWTVerify([]string{constanta.RoleSupervisor, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))
	g.POST("/site-report/worker/calculation", site_report.WorkerCalculation, mw.JWTVerify([]string{constanta.RoleSupervisor, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))
	g.POST("/site-report/worker/save", site_report.SaveWorker, mw.JWTVerify([]string{constanta.RoleSupervisor, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))
	g.POST("/site-report/detail/list", site_report.GetDetailList, mw.JWTVerify([]string{constanta.RoleSupervisor, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))

	// customer
	g.GET("/customer/list", customer.GetList, mw.JWTVerify([]string{constanta.RoleSupervisor, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))
	g.GET("/customer/department/list", customer.GetDepartmentList, mw.JWTVerify([]string{constanta.RoleSupervisor, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))
	g.GET("/customer/department/pic/list", customer.GetDepartmentPicList, mw.JWTVerify([]string{constanta.RoleSupervisor, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))

	// basic price
	g.GET("/basic-price/list", basic_price.GetList, mw.JWTVerify([]string{constanta.RoleSupervisor, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))

	// block
	g.GET("/block/list", block.GetList, mw.JWTVerify([]string{constanta.RoleSupervisor, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))

	// option
	g.GET("/option/list", option.GetList, mw.JWTVerify([]string{constanta.RoleSupervisor, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))

	// qualification
	g.GET("/qualification/list", qualification.GetList, mw.JWTVerify([]string{constanta.RoleSupervisor, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))

	// distant fee
	g.GET("/distant-fee/list", distant_fee.GetList, mw.JWTVerify([]string{constanta.RoleSupervisor, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin}))
}
